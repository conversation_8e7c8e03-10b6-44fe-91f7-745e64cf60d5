services:
  api:
    build:
      context: backend
      target: builder
    container_name: mafia-online-party-game-fastapi
    ports:
      - '${BACKEND_PORT}:8000'
    volumes:
      - ./backend:/build
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    restart: "no"
  web:
    build:
      context: frontend
      dockerfile: Dockerfile
      target: development
    container_name: mafia-online-party-game-web
    ports:
      - "${FRONTEND_PORT}:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      NODE_ENV: development
      VITE_WEBSOCKET_URL: ${VITE_WEBSOCKET_URL}
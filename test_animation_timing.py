#!/usr/bin/env python3
"""
Test script to calculate animation timing for character profiles.
This helps verify that the animation speed is appropriate for human reading.
"""

def calculate_animation_timing():
    """Calculate timing for different text lengths and speeds"""
    
    # Sample character descriptions of different lengths
    sample_descriptions = [
        "<PERSON> runs the town bakery and is famous for her sourdough.",  # Short: ~11 words
        "<PERSON> manages the old library and knows every book by heart, often recommending titles that seem to predict the future.",  # Medium: ~19 words
        "Dr. <PERSON> provides medical care from her clinic and has an uncanny ability to diagnose ailments before patients mention symptoms. She keeps detailed notes on everyone in town and has lived here for over twenty years.",  # Long: ~35 words
    ]
    
    # Different speed settings (milliseconds per word)
    speeds = [
        ("Very Fast", 50),
        ("Fast", 100), 
        ("Optimal (Current)", 150),
        ("Natural Reading", 180),
        ("Comfortable", 250),
        ("Slow", 400)
    ]
    
    print("🎭 Character Profile Animation Timing Analysis")
    print("=" * 60)
    
    for i, description in enumerate(sample_descriptions, 1):
        word_count = len(description.split())
        char_count = len(description)
        
        print(f"\n📝 Sample Description {i}:")
        print(f"Text: \"{description}\"")
        print(f"Words: {word_count} | Characters: {char_count}")
        print("-" * 40)
        
        for speed_name, speed_ms in speeds:
            total_time_ms = word_count * speed_ms
            total_time_s = total_time_ms / 1000
            
            # Calculate reading speed in words per minute
            wpm = (word_count / total_time_s) * 60 if total_time_s > 0 else 0
            
            status = ""
            if wpm > 300:
                status = "⚡ Too fast"
            elif wpm > 200:
                status = "✅ Good pace"
            elif wpm > 150:
                status = "👍 Comfortable"
            elif wpm > 100:
                status = "🐌 Slow"
            else:
                status = "😴 Very slow"
            
            print(f"{speed_name:20} | {speed_ms:3}ms/word | {total_time_s:4.1f}s | {wpm:3.0f} WPM | {status}")
    
    print(f"\n📊 Reading Speed Reference:")
    print(f"• Average reading speed: 200-250 WPM")
    print(f"• Comfortable reading: 150-200 WPM") 
    print(f"• Slow reading: 100-150 WPM")
    print(f"• Animation should be slightly faster than reading for engagement")
    
    print(f"\n🎯 Recommendation:")
    print(f"• Current setting: 150ms/word is optimal")
    print(f"• Provides good pace without being too fast")
    print(f"• Allows users to read comfortably while maintaining engagement")
    print(f"• For 6-second profile display, animation should complete in 2-3 seconds")

def calculate_profile_display_timing():
    """Calculate total timing for profile display including animation"""
    
    print(f"\n⏱️  Profile Display Timing Analysis")
    print("=" * 50)
    
    # Typical profile lengths after our optimization
    typical_lengths = [
        ("Short profile", 8, "Maria runs the bakery."),
        ("Medium profile", 15, "Thomas manages the library and knows every book by heart."),
        ("Long profile", 25, "Dr. Sarah provides medical care and has an uncanny ability to diagnose ailments before patients mention symptoms.")
    ]
    
    animation_speed = 150  # ms per word
    total_display_time = 6000  # 6 seconds total
    
    for profile_type, word_count, example in typical_lengths:
        animation_time = (word_count * animation_speed) / 1000  # seconds
        reading_time = total_display_time / 1000 - animation_time  # remaining time for reading
        
        print(f"\n{profile_type}:")
        print(f"  Example: \"{example}\"")
        print(f"  Words: {word_count}")
        print(f"  Animation time: {animation_time:.1f}s")
        print(f"  Reading time: {reading_time:.1f}s")
        print(f"  Total display: {total_display_time/1000:.1f}s")
        
        if animation_time > 3:
            print(f"  ⚠️  Animation might be too long")
        elif animation_time < 1:
            print(f"  ⚠️  Animation might be too short")
        else:
            print(f"  ✅ Good timing balance")

if __name__ == "__main__":
    calculate_animation_timing()
    calculate_profile_display_timing()
    
    print(f"\n🎉 Analysis completed!")
    print(f"\nKey findings:")
    print(f"• 150ms per word provides optimal reading pace (~240 WPM)")
    print(f"• Animation completes in 1-4 seconds depending on text length")
    print(f"• Leaves 2-5 seconds for comfortable reading")
    print(f"• Balances engagement with readability")

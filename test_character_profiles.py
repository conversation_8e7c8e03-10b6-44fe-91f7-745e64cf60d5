#!/usr/bin/env python3
"""
Test script to verify character profile generation improvements.
This script tests the modified character generator to ensure:
1. Descriptions are concise but complete
2. No artificial truncation occurs
3. Generated text fits well in the UI
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.character_generator import CharacterGenerator
from app.services.llm_client import DeepSeekClient

def test_character_generation():
    """Test character profile generation with new settings"""
    
    # Mock LLM client for testing
    class MockLLMClient:
        def generate_text(self, prompt, max_tokens=150, temperature=0.8):
            # Simulate shorter, complete responses
            responses = {
                "Baker": "<PERSON> runs the town bakery and is famous for her sourdough that locals say has magical properties. She arrived from Italy twenty years ago with secret family recipes and a mysterious past.",
                "Librarian": "<PERSON> manages the old library and knows every book by heart, often recommending titles that seem to predict the future. He moved here after inheriting the building from his eccentric aunt.",
                "Doctor": "Dr. <PERSON> provides medical care from her clinic and has an uncanny ability to diagnose ailments before patients mention symptoms. She keeps detailed notes on everyone in town.",
                "Mechanic": "<PERSON> fixes cars at his garage and can identify any engine problem just by listening. He's lived here his whole life and knows all the town's mechanical secrets."
            }
            
            # Extract profession from prompt
            for profession, response in responses.items():
                if profession.lower() in prompt.lower():
                    return response
            
            return "A mysterious townsperson with secrets to uncover."
    
    # Create generator with mock client
    mock_client = MockLLMClient()
    generator = CharacterGenerator(mock_client)
    
    # Test data
    test_players = [
        {"player_id": "1", "name": "Maria"},
        {"player_id": "2", "name": "Thomas"},
        {"player_id": "3", "name": "Sarah"},
        {"player_id": "4", "name": "Jake"}
    ]
    
    print("🎭 Testing Character Profile Generation")
    print("=" * 50)
    
    # Generate profiles
    profiles = generator.generate_profiles_for_players(test_players)
    
    for i, profile in enumerate(profiles, 1):
        print(f"\n📋 Profile {i}:")
        print(f"Name: {profile.name}")
        print(f"Profession: {profile.profession} {profile.emoji}")
        print(f"Description: {profile.description}")
        print(f"Character count: {len(profile.description)}")
        print("-" * 40)
        
        # Verify no truncation occurred (no "..." at the end)
        if profile.description.endswith("..."):
            print("❌ WARNING: Description appears to be truncated!")
        else:
            print("✅ Description is complete")
    
    print(f"\n📊 Summary:")
    print(f"Total profiles generated: {len(profiles)}")
    avg_length = sum(len(p.description) for p in profiles) / len(profiles)
    print(f"Average description length: {avg_length:.1f} characters")
    
    # Check if descriptions are reasonable length (not too long, not too short)
    for profile in profiles:
        length = len(profile.description)
        if length < 50:
            print(f"⚠️  {profile.name}'s description might be too short ({length} chars)")
        elif length > 300:
            print(f"⚠️  {profile.name}'s description might be too long ({length} chars)")
        else:
            print(f"✅ {profile.name}'s description length is good ({length} chars)")

def test_prompt_changes():
    """Test the new prompt to ensure it generates appropriate content"""
    
    generator = CharacterGenerator(None)  # We only need the prompt method
    
    print("\n🎯 Testing New Prompt Structure")
    print("=" * 50)
    
    prompt = generator._create_character_prompt("Alice", "Baker")
    
    print("Generated prompt:")
    print(prompt)
    
    # Check if prompt contains the right instructions
    checks = [
        ("2-3 sentences", "2-3 sentences" in prompt),
        ("under 200 characters", "under 200 characters" in prompt),
        ("concise", "concise" in prompt),
        ("memorable but concise", "memorable but concise" in prompt)
    ]
    
    print("\n📝 Prompt Analysis:")
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}: {'Found' if passed else 'Missing'}")

if __name__ == "__main__":
    test_character_generation()
    test_prompt_changes()
    
    print("\n🎉 Testing completed!")
    print("\nKey improvements made:")
    print("• Reduced max_tokens from 300 to 150")
    print("• Modified prompt to request 2-3 sentences instead of 3-4")
    print("• Removed 400-character truncation limit")
    print("• Added length guidance (under 200 chars) to prompt")
    print("• Maintained full LLM-generated content display")
